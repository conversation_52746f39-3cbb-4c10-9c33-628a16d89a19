<encoding>
DE0E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1776</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
DE2E0710 00C000
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1777</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DE4E017F C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1778</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>11000100011011010001100100001000010100000000101100011111111100000001010010000011000001111000101000101011111001100010101011010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>1B46</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>FF01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms2000/>
    </t-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <t-313>7</t-313>
    <n-313>
      <s100/>
    </n-313>
    <t-314>
      <s6/>
    </t-314>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
</SysInfoType1>

<encoding>
DE60
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1779</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DE8E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1780</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
DEA0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1781</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DEC0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1782</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DEEE0376 1F960D81 44FC6000 50010000 11094E
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1783</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00011111100101100000110110000001010001001111110001100000000000000101000000000001000000000000000000010001000010010100111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1F960D81 44FC6000 50010000 11094E
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0111111001011000001101100000</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0>
        <q-HYST-2-S>2</q-HYST-2-S>
      </cpich-Ec-N0>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>8</s-Intrasearch>
        <s-Intersearch>8</s-Intersearch>
        <s-SearchHCS>-53</s-SearchHCS>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-53</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-24</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>2</t-Reselection-S>
    <hcs-ServingCellInformation/>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
DF0E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1784</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
DF2E2C43 B38111D0 24541A42 A3880060 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1785</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110011100000010001000111010000001001000101010000011010010000101010</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B38111D0 24541A42 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th256/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
DF40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1786</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DF6E1F8B 61FD0416 94E40C70 0AF39030 623920A4 C160
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1787</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <extensionType/>
          </sib-Type>
          <sib-Data-variable>01100001111111010000010000010110100101001110010000001100011100000000101011110011100100000011000001100010001110010010000010100100110000010110</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
61FD0416 94E40C70 0AF39030 623920A4 C160
</encoding>

<SysInfoType19>
  <utra-PriorityInfoList>
    <utra-ServingCell>
      <priority>3</priority>
      <s-PrioritySearch1>31</s-PrioritySearch1>
      <threshServingLow>8</threshServingLow>
    </utra-ServingCell>
  </utra-PriorityInfoList>
  <eutra-FrequencyAndPriorityInfoList>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>5780</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>3</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>700</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>3</threshXhigh>
      <threshXlow>0</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
  </eutra-FrequencyAndPriorityInfoList>
  <v920NonCriticalExtensions>
    <sysInfoType19-v920ext>
      <utra-PriorityInfoList-v920ext/>
      <eutra-FrequencyAndPriorityInfoList-v920ext>
        <EUTRA-FrequencyAndPriorityInfo-v920ext/>
        <EUTRA-FrequencyAndPriorityInfo-v920ext>
          <qqualMinEUTRA>-30</qqualMinEUTRA>
          <threshXhigh2>16</threshXhigh2>
          <threshXlow2>10</threshXlow2>
        </EUTRA-FrequencyAndPriorityInfo-v920ext>
      </eutra-FrequencyAndPriorityInfoList-v920ext>
    </sysInfoType19-v920ext>
    <va80NonCriticalExtensions>
      <sysInfoType19-va80ext>
        <multipleEutraFrequencyInfoList>
          <MultipleEUTRAFrequencyBandInfo>
            <multipleEUTRAFrequencyBandIndicatorlist>
              <RadioFrequencyBandEUTRA>12</RadioFrequencyBandEUTRA>
            </multipleEUTRAFrequencyBandIndicatorlist>
          </MultipleEUTRAFrequencyBandInfo>
          <MultipleEUTRAFrequencyBandInfo/>
        </multipleEutraFrequencyInfoList>
      </sysInfoType19-va80ext>
    </va80NonCriticalExtensions>
  </v920NonCriticalExtensions>
</SysInfoType19>

<encoding>
DF8E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1788</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
DFA0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1789</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DFC0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1790</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
DFEE1B33 81787320 017B60
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1791</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <schedulingBlock1/>
          </sib-Type>
          <sib-Data-variable>1000000101111000011100110010000000000001011110110110</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E00E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1792</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E02E0710 00C000
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1793</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E04E017F C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1794</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>11000100011011010001100100001000010100000000101100011111111100000001010010000011000001111000101000101011111001100010101011010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>1B46</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>FF01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms2000/>
    </t-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <t-313>7</t-313>
    <n-313>
      <s100/>
    </n-313>
    <t-314>
      <s6/>
    </t-314>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
</SysInfoType1>

<encoding>
******** 403AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F640
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1795</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>011000110100000000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110010000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E08E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1796</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E0A45043 8C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1797</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>010000111000110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E0C6517C C4300B6D 83002184 4A058576 0186AF40 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1798</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>11000100001100000000101101101101100000110000000000100001100001000100101000000101100001010111011000000001100001101010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E0EE0376 1F960D81 44FC6000 50010000 11094E
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1799</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00011111100101100000110110000001010001001111110001100000000000000101000000000001000000000000000000010001000010010100111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1F960D81 44FC6000 50010000 11094E
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0111111001011000001101100000</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0>
        <q-HYST-2-S>2</q-HYST-2-S>
      </cpich-Ec-N0>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>8</s-Intrasearch>
        <s-Intersearch>8</s-Intersearch>
        <s-SearchHCS>-53</s-SearchHCS>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-53</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-24</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>2</t-Reselection-S>
    <hcs-ServingCellInformation/>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
E10E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1800</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E12E2C43 B38111D0 24541A42 A3880060 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1801</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110011100000010001000111010000001001000101010000011010010000101010</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B38111D0 24541A42 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th256/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
E140
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1802</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E160
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1803</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E18E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1804</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E1A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1805</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E1C2B3F9 C00EFE70 2CC4092D 0A274040 15358080 086B0223 42B87010 0123A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1806</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <seg-Count>4</seg-Count>
        <sib-Data-fixed>111110011100000000001110111111100111000000101100110001000000100100101101000010100010011101000000010000000001010100110101100000001000000000001000011010110000001000100011010000101011100001110000000100000000000100100011101000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E1E4B012 1A058B81 45604AE8 11FA153D 80800BDB 4800C585 8595410C 09D848
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1807</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>000100100001101000000101100010111000000101000101011000000100101011101000000100011111101000010101001111011000000010000000000010111101101101001000000000001100010110000101100001011001010101000001000011000000100111011000010010</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E20E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1808</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E22E0710 00C000
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1809</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E24E017F C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1810</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>11000100011011010001100100001000010100000000101100011111111100000001010010000011000001111000101000101011111001100010101011010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>1B46</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>FF01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms2000/>
    </t-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <t-313>7</t-313>
    <n-313>
      <s100/>
    </n-313>
    <t-314>
      <s6/>
    </t-314>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
</SysInfoType1>

<encoding>
E264B1EA 21400E07 3FE71E2A 4C125FE0 781302FF 23C09817 FA1E04C0 BFD8F0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1811</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-fixed>111010100010000101000000000011100000011100111111111001110001111000101010010011000001001001011111111000000111100000010011000000101111111100100011110000001001100000010111111110100001111000000100110000001011111111011000111100</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E28E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1812</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E2A6B284 09817FC1 E04C0BFE 8F026281 C1204000 80
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1813</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>3</segmentIndex>
        <sib-Data-variable>0000100110000001011111111100000111100000010011000000101111111110100011110000001001100010100000011100000100100000010000000000000010000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
F9C00EFE 702CC409 2D0A2740 40153580 80086B02 2342B870 100123A0 4868162E 
0515812B A047E854 F602002F 6D200316 16165504 3027612E A21400E0 73FE71E2 
A4C125FE 0781302F F23C0981 7FA1E04C 0BFD8F02 605FF078 1302FFA3 C098A070 
48100020 
</encoding>

<SysInfoType11>
  <sib12indicator>
    <true/>
  </sib12indicator>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0>
            <intraFreqMeasurementSysInfo>
              <intraFreqCellInfoSI-List>
                <removedIntraFreqCellList>
                  <removeNoIntraFreqCells/>
                </removedIntraFreqCellList>
                <newIntraFreqCellList>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>408</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>150</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>78</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-RxlevMin>-58</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>309</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-RxlevMin>-58</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>53</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>70</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>451</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-RxlevMin>-58</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>142</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>134</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>395</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>277</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>174</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>126</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>317</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-RxlevMin>-58</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>493</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                </newIntraFreqCellList>
              </intraFreqCellInfoSI-List>
              <intraFreqMeasQuantity>
                <filterCoefficient>
                  <fc2/>
                </filterCoefficient>
                <modeSpecificInfo>
                  <fdd>
                    <intraFreqMeasQuantity-FDD>
                      <cpich-Ec-N0/>
                    </intraFreqMeasQuantity-FDD>
                  </fdd>
                </modeSpecificInfo>
              </intraFreqMeasQuantity>
              <intraFreqReportingQuantityForRACH>
                <sfn-SFN-OTD-Type>
                  <noReport/>
                </sfn-SFN-OTD-Type>
                <modeSpecificInfo>
                  <fdd>
                    <intraFreqRepQuantityRACH-FDD>
                      <cpich-EcN0/>
                    </intraFreqRepQuantityRACH-FDD>
                  </fdd>
                </modeSpecificInfo>
              </intraFreqReportingQuantityForRACH>
              <maxReportedCellsOnRACH>
                <currentCell/>
              </maxReportedCellsOnRACH>
              <reportingInfoForCellDCH>
                <intraFreqReportingQuantity>
                  <activeSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </activeSetReportingQuantities>
                  <monitoredSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </monitoredSetReportingQuantities>
                  <detectedSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </detectedSetReportingQuantities>
                </intraFreqReportingQuantity>
                <measurementReportingMode>
                  <measurementReportTransferMode>
                    <acknowledgedModeRLC/>
                  </measurementReportTransferMode>
                  <periodicalOrEventTrigger>
                    <eventTrigger/>
                  </periodicalOrEventTrigger>
                </measurementReportingMode>
                <reportCriteria>
                  <intraFreqReportingCriteria>
                    <eventCriteriaList>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1a>
                            <triggeringCondition>
                              <detectedSetAndMonitoredSetCells/>
                            </triggeringCondition>
                            <reportingRange>6</reportingRange>
                            <w>0</w>
                            <reportDeactivationThreshold>
                              <t4/>
                            </reportDeactivationThreshold>
                            <reportingAmount>
                              <ra-Infinity/>
                            </reportingAmount>
                            <reportingInterval>
                              <ri1/>
                            </reportingInterval>
                          </e1a>
                        </event>
                        <hysteresis>0</hysteresis>
                        <timeToTrigger>
                          <ttt200/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <allActivePlusMonitoredAndOrDetectedSet>
                            <viactCellsPlus3/>
                          </allActivePlusMonitoredAndOrDetectedSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1b>
                            <triggeringCondition>
                              <activeSetCellsOnly/>
                            </triggeringCondition>
                            <reportingRange>10</reportingRange>
                            <w>0</w>
                          </e1b>
                        </event>
                        <hysteresis>0</hysteresis>
                        <timeToTrigger>
                          <ttt2560/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <withinActiveSet>
                            <e4/>
                          </withinActiveSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1d/>
                        </event>
                        <hysteresis>15</hysteresis>
                        <timeToTrigger>
                          <ttt2560/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <allActivePlusMonitoredAndOrDetectedSet>
                            <viactCellsPlus1/>
                          </allActivePlusMonitoredAndOrDetectedSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                    </eventCriteriaList>
                  </intraFreqReportingCriteria>
                </reportCriteria>
              </reportingInfoForCellDCH>
            </intraFreqMeasurementSysInfo>
            <interFreqMeasurementSysInfo>
              <interFreqCellInfoSI-List>
                <removedInterFreqCellList>
                  <removeNoInterFreqCells/>
                </removedInterFreqCellList>
                <newInterFreqCellList>
                  <NewInterFreqCellSI-ECN0>
                    <frequencyInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <uarfcn-DL>9737</uarfcn-DL>
                        </fdd>
                      </modeSpecificInfo>
                    </frequencyInfo>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>504</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>505</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>506</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>507</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>508</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>509</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>-3</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-12</q-QualMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                </newInterFreqCellList>
              </interFreqCellInfoSI-List>
            </interFreqMeasurementSysInfo>
          </cpich-Ec-N0>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
  </measurementControlSysInfo>
  <v4b0NonCriticalExtensions>
    <v590NonCriticalExtension>
      <sysInfoType11-v590ext>
        <newIntraFrequencyCellInfoList-v590ext>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext>
            <deltaQrxlevmin>-2</deltaQrxlevmin>
          </CellSelectReselectInfo-v590ext>
          <CellSelectReselectInfo-v590ext>
            <deltaQrxlevmin>-2</deltaQrxlevmin>
          </CellSelectReselectInfo-v590ext>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext>
            <deltaQrxlevmin>-2</deltaQrxlevmin>
          </CellSelectReselectInfo-v590ext>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext/>
          <CellSelectReselectInfo-v590ext>
            <deltaQrxlevmin>-2</deltaQrxlevmin>
          </CellSelectReselectInfo-v590ext>
          <CellSelectReselectInfo-v590ext/>
        </newIntraFrequencyCellInfoList-v590ext>
      </sysInfoType11-v590ext>
    </v590NonCriticalExtension>
  </v4b0NonCriticalExtensions>
</SysInfoType11>

<encoding>
E2C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1814</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E2EE0376 1F960D81 44FC6000 50010000 11094E
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1815</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00011111100101100000110110000001010001001111110001100000000000000101000000000001000000000000000000010001000010010100111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1F960D81 44FC6000 50010000 11094E
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0111111001011000001101100000</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0>
        <q-HYST-2-S>2</q-HYST-2-S>
      </cpich-Ec-N0>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>8</s-Intrasearch>
        <s-Intersearch>8</s-Intersearch>
        <s-SearchHCS>-53</s-SearchHCS>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-53</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-24</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>2</t-Reselection-S>
    <hcs-ServingCellInformation/>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
E30E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1816</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E32E2C43 B38111D0 24541A42 A3880060 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1817</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110011100000010001000111010000001001000101010000011010010000101010</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B38111D0 24541A42 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th256/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
E340
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1818</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E360
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1819</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E38E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1820</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E3A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1821</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E3C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1822</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E3E0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1823</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E40E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1824</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E42E0710 00C000
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1825</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E44E017F C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1826</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>11000100011011010001100100001000010100000000101100011111111100000001010010000011000001111000101000101011111001100010101011010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>1B46</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>FF01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms2000/>
    </t-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <t-313>7</t-313>
    <n-313>
      <s100/>
    </n-313>
    <t-314>
      <s6/>
    </t-314>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
</SysInfoType1>

<encoding>
******** 403AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F640
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1827</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>011000110100000000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110010000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E48E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1828</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E4A45043 8C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1829</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>010000111000110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E4C6517C C4300B6D 83002184 4A058576 0186AF40 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1830</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>11000100001100000000101101101101100000110000000000100001100001000100101000000101100001010111011000000001100001101010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E4EE0376 1F960D81 44FC6000 50010000 11094E
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1831</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00011111100101100000110110000001010001001111110001100000000000000101000000000001000000000000000000010001000010010100111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1F960D81 44FC6000 50010000 11094E
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0111111001011000001101100000</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0>
        <q-HYST-2-S>2</q-HYST-2-S>
      </cpich-Ec-N0>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>8</s-Intrasearch>
        <s-Intersearch>8</s-Intersearch>
        <s-SearchHCS>-53</s-SearchHCS>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-53</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-24</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>2</t-Reselection-S>
    <hcs-ServingCellInformation/>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
E50E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1832</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E52E2C43 B38111D0 24541A42 A3880060 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1833</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110011100000010001000111010000001001000101010000011010010000101010</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B38111D0 24541A42 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th256/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
E540
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1834</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E560
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1835</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E58E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1836</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E5A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1837</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E5C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1838</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E5E0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1839</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E60E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1840</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E62E0710 00C000
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1841</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E64E017F C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1842</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>11000100011011010001100100001000010100000000101100011111111100000001010010000011000001111000101000101011111001100010101011010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
C46D1908 500B1FF0 1483078A 2BE62AD0 
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>1B46</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>FF01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms2000/>
    </t-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <t-313>7</t-313>
    <n-313>
      <s100/>
    </n-313>
    <t-314>
      <s6/>
    </t-314>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
</SysInfoType1>

<encoding>
E660
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1843</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E68E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1844</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E6A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1845</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1846</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E6EE0376 1F960D81 44FC6000 50010000 11094E
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1847</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00011111100101100000110110000001010001001111110001100000000000000101000000000001000000000000000000010001000010010100111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1F960D81 44FC6000 50010000 11094E
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0111111001011000001101100000</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0>
        <q-HYST-2-S>2</q-HYST-2-S>
      </cpich-Ec-N0>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>8</s-Intrasearch>
        <s-Intersearch>8</s-Intersearch>
        <s-SearchHCS>-53</s-SearchHCS>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-53</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-24</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>2</t-Reselection-S>
    <hcs-ServingCellInformation/>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
E70E00A9 60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1848</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01100000110001000010100000100000010110101110001011111110000011110010000011001000010100000110111001000110001001000001100110000010001010100111001101010011100010000000110001</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
60C42820 5AE2FE0F 20C8506E 46241982 2A735388 0C40
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>7</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>4</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>4</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>243</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>7</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>2</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep64>3</rep64>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>4</segCount>
          <sib-Pos>
            <rep128>14</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so8/>
            <so4/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
E72E2C43 B38111D0 24541A42 A3880060 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1849</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110011100000010001000111010000001001000101010000011010010000101010</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000000110000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B38111D0 24541A42 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>4</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <true/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th256/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
E740
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1850</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E76E1F8B 61FD0416 94E40C70 0AF39030 623920A4 C160
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1851</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <extensionType/>
          </sib-Type>
          <sib-Data-variable>01100001111111010000010000010110100101001110010000001100011100000000101011110011100100000011000001100010001110010010000010100100110000010110</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
61FD0416 94E40C70 0AF39030 623920A4 C160
</encoding>

<SysInfoType19>
  <utra-PriorityInfoList>
    <utra-ServingCell>
      <priority>3</priority>
      <s-PrioritySearch1>31</s-PrioritySearch1>
      <threshServingLow>8</threshServingLow>
    </utra-ServingCell>
  </utra-PriorityInfoList>
  <eutra-FrequencyAndPriorityInfoList>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>5780</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>3</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>700</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>3</threshXhigh>
      <threshXlow>0</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
  </eutra-FrequencyAndPriorityInfoList>
  <v920NonCriticalExtensions>
    <sysInfoType19-v920ext>
      <utra-PriorityInfoList-v920ext/>
      <eutra-FrequencyAndPriorityInfoList-v920ext>
        <EUTRA-FrequencyAndPriorityInfo-v920ext/>
        <EUTRA-FrequencyAndPriorityInfo-v920ext>
          <qqualMinEUTRA>-30</qqualMinEUTRA>
          <threshXhigh2>16</threshXhigh2>
          <threshXlow2>10</threshXlow2>
        </EUTRA-FrequencyAndPriorityInfo-v920ext>
      </eutra-FrequencyAndPriorityInfoList-v920ext>
    </sysInfoType19-v920ext>
    <va80NonCriticalExtensions>
      <sysInfoType19-va80ext>
        <multipleEutraFrequencyInfoList>
          <MultipleEUTRAFrequencyBandInfo>
            <multipleEUTRAFrequencyBandIndicatorlist>
              <RadioFrequencyBandEUTRA>12</RadioFrequencyBandEUTRA>
            </multipleEUTRAFrequencyBandIndicatorlist>
          </MultipleEUTRAFrequencyBandInfo>
          <MultipleEUTRAFrequencyBandInfo/>
        </multipleEutraFrequencyInfoList>
      </sysInfoType19-va80ext>
    </va80NonCriticalExtensions>
  </v920NonCriticalExtensions>
</SysInfoType19>

