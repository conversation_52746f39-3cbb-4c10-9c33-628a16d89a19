Overview:
========
The Octasic License Updater (OLU) tool is used to write the licensing information in the BTS platforms.

Prerequisite:
============
The Octpodd included in Octasic BSP is required to run the OLU tool.

Usage:
======
- Copy the licensing tool and the provided licensing information file on the PHYTEC
- Run the licensing tool as following:

  licensing [database_filename] <ip_address>
    * database_filename: Board signature filename.
    * ip_address: Optional POD IP address, default is 127.0.0.1.
