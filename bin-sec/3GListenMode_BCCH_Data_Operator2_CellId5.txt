<encoding>
340E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>416</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
342E2367 1793C194 1B5A0506 9020010C A738800A 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>417</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111100000110010100000110110101101000000101000001101001000000100000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001010000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793C194 1B5A0506 9020010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111000001100101</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-20</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
344E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>418</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
3463E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F550
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>419</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110101010100</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
348E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>420</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
34A5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>421</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
34C7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>422</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
34EE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>423</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
350E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>424</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
352E2367 1793C194 1B5A0506 9020010C A738800A 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>425</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111100000110010100000110110101101000000101000001101001000000100000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001010000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793C194 1B5A0506 9020010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111000001100101</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-20</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
3540
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>426</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
3560
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>427</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
358E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>428</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
35A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>429</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
35C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>430</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
35E0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>431</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
360E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>432</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
362E2367 1793C194 1B5A0506 9020010C A738800A 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>433</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111100000110010100000110110101101000000101000001101001000000100000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001010000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793C194 1B5A0506 9020010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111000001100101</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-20</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
364E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>434</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
3663E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F550
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>435</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110101010100</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
368E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>436</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
36A5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>437</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
36C7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>438</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
36EE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>439</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
370E00BB 70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>440</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01110000110001000010010011000000010110101010001011111110000000100110000011001000010100000100011110100100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
70C424C0 5AA2FE02 60C85047 A4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>8</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>39</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>3</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>1</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

