<encoding>
468E00BB 30C424C0 5AA2FE02 30C84047 AC466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>564</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>00110000110001000010010011000000010110101010001011111110000000100011000011001000010000000100011110101100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
30C424C0 5AA2FE02 30C84047 AC466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>4</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>2</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>36</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>2</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

