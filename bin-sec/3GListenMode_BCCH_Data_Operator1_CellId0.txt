<encoding>
958E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1196</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
95A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1197</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
95C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1198</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
95E0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1199</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
960E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1200</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
962E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1201</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
964E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1202</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
9663E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1203</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110110000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
968E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1204</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
96A5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1205</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
96C7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1206</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
96EE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1207</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
970E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1208</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
972E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1209</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9740
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1210</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
976E0213 03AEB0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1211</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType2/>
          </sib-Type>
          <sib-Data-variable>00000011101011101011</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
978E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1212</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
97AE1F87 41FD0813 ABA40470 11FB1011 C08CAE40 47
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1213</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <extensionType/>
          </sib-Type>
          <sib-Data-variable>0100000111111101000010000001001110101011101001000000010001110000000100011111101100010000000100011100000010001100101011100100000001000111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
41FD0813 ABA40470 11FB1011 C08CAE40 47
</encoding>

<SysInfoType19>
  <utra-PriorityInfoList>
    <utra-ServingCell>
      <priority>3</priority>
      <s-PrioritySearch1>31</s-PrioritySearch1>
      <threshServingLow>8</threshServingLow>
    </utra-ServingCell>
  </utra-PriorityInfoList>
  <eutra-FrequencyAndPriorityInfoList>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>5035</earfcn>
      <priority>5</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>1150</earfcn>
      <priority>6</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>2250</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
  </eutra-FrequencyAndPriorityInfoList>
</SysInfoType19>

<encoding>
97C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1214</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
97EE1B46 8420BDAC 0DE4002F AC
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1215</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <schedulingBlock1/>
          </sib-Type>
          <sib-Data-variable>10000100001000001011110110101100000011011110010000000000001011111010110</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
980E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1216</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
982E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1217</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
984E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1218</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
9863E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1219</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110110000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
988E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1220</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
98A5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1221</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
98C7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1222</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
98EE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1223</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
990E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1224</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
992E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1225</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9942B8B9 001EFEF8 24CC082F 0227C095 F02A7C08 F1024F40 A7902BFC 09C900
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1226</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <seg-Count>9</seg-Count>
        <sib-Data-fixed>101110010000000000011110111111101111100000100100110011000000100000101111000000100010011111000000100101011111000000101010011111000000100011110001000000100100111101000000101001111001000000101011111111000000100111001001000000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9964B097 D023B409 F10256C0 98902BEC 0B850296 C09DD026 E40B9B02 47C0AC
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1227</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>100101111101000000100011101101000000100111110001000000100101011011000000100110001001000000101011111011000000101110000101000000101001011011000000100111011101000000100110111001000000101110011011000000100100011111000000101011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
998E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1228</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
99A4B1E4 08410293 4091B02D FC09A102 ACC0A8D0 29FC0A3F 4C00C585 859540
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1229</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-fixed>111001000000100001000001000000101001001101000000100100011011000000101101111111000000100110100001000000101010110011000000101010001101000000101001111111000000101000111111010011000000000011000101100001011000010110010101010000</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
99C4B243 027694EA 21804D07 3FE71E12 052C5F9E 8B0A1469 1C2FDF45 850A34
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1230</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>3</segmentIndex>
        <sib-Data-fixed>010000110000001001110110100101001110101000100001100000000100110100000111001111111110011100011110000100100000010100101100010111111001111010001011000010100001010001101001000111000010111111011111010001011000010100001010001101</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
99E4B323 85FDE8B0 A14691F4 050EA8EC 94E10287 54765BF2 5143AA3B 2BB8D8
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1231</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>4</segmentIndex>
        <sib-Data-fixed>001000111000010111111101111010001011000010100001010001101001000111110100000001010000111010101000111011001001010011100001000000101000011101010100011101100101101111110010010100010100001110101010001110110010101110111000110110</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9A0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1232</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9A2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1233</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9A4E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1234</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
9A63E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1235</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110110000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9A8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1236</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9AA5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1237</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9AC7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1238</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9AEE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1239</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
9B0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1240</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9B2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1241</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9B44B428 754765EF 10143AA3 B293870A 1D51D917 C3C50EA8 EC83E262 875474
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1242</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>5</segmentIndex>
        <sib-Data-fixed>001010000111010101000111011001011110111100010000000101000011101010100011101100101001001110000111000010100001110101010001110110010001011111000011110001010000111010101000111011001000001111100010011000101000011101010100011101</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9B64B597 FC7450EA 8ECA8E2E 28754764 5712143A A3B2137E 8A1D51D9 2DC2C4
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1243</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>6</segmentIndex>
        <sib-Data-fixed>100101111111110001110100010100001110101010001110110010101000111000101110001010000111010101000111011001000101011100010010000101000011101010100011101100100001001101111110100010100001110101010001110110010010110111000010110001</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9B8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1244</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9BA4B643 AA3B2277 E0A1D51D 91BCA850 EA8EC8EE 3E287547 646EFD14 3AA3B0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1245</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>7</segmentIndex>
        <sib-Data-fixed>010000111010101000111011001000100111011111100000101000011101010100011101100100011011110010101000010100001110101010001110110010001110111000111110001010000111010101000111011001000110111011111101000101000011101010100011101100</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9BC6B712 94E1A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1246</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>8</segmentIndex>
        <sib-Data-variable>1001010011100001101</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B9001EFE F824CC08 2F0227C0 95F02A7C 08F1024F 40A7902B FC09C902 5F408ED0 
27C4095B 026240AF B02E140A 5B027740 9B902E6C 091F02BE 40841029 34091B02 
DFC09A10 2ACC0A8D 029FC0A3 F4C00C58 58595410 C09DA53A 88601341 CFF9C784 
814B17E7 A2C2851A 470BF7D1 61428D23 85FDE8B0 A14691F4 050EA8EC 94E10287 
54765BF2 5143AA3B 2BB8D8A1 D51D97BC 4050EA8E CA4E1C28 7547645F 0F143AA3 
B20F898A 1D51D97F C7450EA8 ECA8E2E2 87547645 712143AA 3B2137E8 A1D51D92 
DC2C50EA 8EC89DF8 28754764 6F2A143A A3B23B8F 8A1D51D9 1BBF450E A8EC94E1 
A0
</encoding>

<SysInfoType11>
  <sib12indicator>
    <true/>
  </sib12indicator>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0>
            <intraFreqMeasurementSysInfo>
              <intraFreqCellInfoSI-List>
                <removedIntraFreqCellList>
                  <removeNoIntraFreqCells/>
                </removedIntraFreqCellList>
                <newIntraFreqCellList>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>153</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>23</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>79</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>175</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>335</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>120</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>158</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>316</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>383</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>228</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>190</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>118</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>248</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>173</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>196</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>381</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>450</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>301</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>238</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>220</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>461</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>143</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>380</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>32</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>294</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>141</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>447</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>208</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>345</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>326</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>319</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                  <NewIntraFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>287</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                    </cellInfo>
                  </NewIntraFreqCellSI-ECN0>
                </newIntraFreqCellList>
              </intraFreqCellInfoSI-List>
              <intraFreqMeasQuantity>
                <filterCoefficient>
                  <fc3/>
                </filterCoefficient>
                <modeSpecificInfo>
                  <fdd>
                    <intraFreqMeasQuantity-FDD>
                      <cpich-Ec-N0/>
                    </intraFreqMeasQuantity-FDD>
                  </fdd>
                </modeSpecificInfo>
              </intraFreqMeasQuantity>
              <intraFreqReportingQuantityForRACH>
                <sfn-SFN-OTD-Type>
                  <noReport/>
                </sfn-SFN-OTD-Type>
                <modeSpecificInfo>
                  <fdd>
                    <intraFreqRepQuantityRACH-FDD>
                      <cpich-EcN0/>
                    </intraFreqRepQuantityRACH-FDD>
                  </fdd>
                </modeSpecificInfo>
              </intraFreqReportingQuantityForRACH>
              <maxReportedCellsOnRACH>
                <currentCell/>
              </maxReportedCellsOnRACH>
              <reportingInfoForCellDCH>
                <intraFreqReportingQuantity>
                  <activeSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </activeSetReportingQuantities>
                  <monitoredSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </monitoredSetReportingQuantities>
                  <detectedSetReportingQuantities>
                    <dummy>
                      <noReport/>
                    </dummy>
                    <cellIdentity-reportingIndicator>
                      <false/>
                    </cellIdentity-reportingIndicator>
                    <cellSynchronisationInfoReportingIndicator>
                      <true/>
                    </cellSynchronisationInfoReportingIndicator>
                    <modeSpecificInfo>
                      <fdd>
                        <cpich-Ec-N0-reportingIndicator>
                          <true/>
                        </cpich-Ec-N0-reportingIndicator>
                        <cpich-RSCP-reportingIndicator>
                          <true/>
                        </cpich-RSCP-reportingIndicator>
                        <pathloss-reportingIndicator>
                          <false/>
                        </pathloss-reportingIndicator>
                      </fdd>
                    </modeSpecificInfo>
                  </detectedSetReportingQuantities>
                </intraFreqReportingQuantity>
                <measurementReportingMode>
                  <measurementReportTransferMode>
                    <acknowledgedModeRLC/>
                  </measurementReportTransferMode>
                  <periodicalOrEventTrigger>
                    <eventTrigger/>
                  </periodicalOrEventTrigger>
                </measurementReportingMode>
                <reportCriteria>
                  <intraFreqReportingCriteria>
                    <eventCriteriaList>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1a>
                            <triggeringCondition>
                              <detectedSetAndMonitoredSetCells/>
                            </triggeringCondition>
                            <reportingRange>6</reportingRange>
                            <w>0</w>
                            <reportDeactivationThreshold>
                              <t4/>
                            </reportDeactivationThreshold>
                            <reportingAmount>
                              <ra-Infinity/>
                            </reportingAmount>
                            <reportingInterval>
                              <ri1/>
                            </reportingInterval>
                          </e1a>
                        </event>
                        <hysteresis>4</hysteresis>
                        <timeToTrigger>
                          <ttt240/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <allActivePlusMonitoredAndOrDetectedSet>
                            <viactCellsPlus3/>
                          </allActivePlusMonitoredAndOrDetectedSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1b>
                            <triggeringCondition>
                              <activeSetCellsOnly/>
                            </triggeringCondition>
                            <reportingRange>12</reportingRange>
                            <w>0</w>
                          </e1b>
                        </event>
                        <hysteresis>4</hysteresis>
                        <timeToTrigger>
                          <ttt1280/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <withinActiveSet>
                            <e4/>
                          </withinActiveSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                      <IntraFreqEventCriteria>
                        <event>
                          <e1d/>
                        </event>
                        <hysteresis>15</hysteresis>
                        <timeToTrigger>
                          <ttt2560/>
                        </timeToTrigger>
                        <reportingCellStatus>
                          <allActivePlusMonitoredAndOrDetectedSet>
                            <viactCellsPlus1/>
                          </allActivePlusMonitoredAndOrDetectedSet>
                        </reportingCellStatus>
                      </IntraFreqEventCriteria>
                    </eventCriteriaList>
                  </intraFreqReportingCriteria>
                </reportCriteria>
              </reportingInfoForCellDCH>
            </intraFreqMeasurementSysInfo>
            <interFreqMeasurementSysInfo>
              <interFreqCellInfoSI-List>
                <removedInterFreqCellList>
                  <removeNoInterFreqCells/>
                </removedInterFreqCellList>
                <newInterFreqCellList>
                  <NewInterFreqCellSI-ECN0>
                    <frequencyInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <uarfcn-DL>662</uarfcn-DL>
                        </fdd>
                      </modeSpecificInfo>
                    </frequencyInfo>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>487</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>30</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <q-Offset1S-N>-40</q-Offset1S-N>
                        <q-Offset2S-N>-40</q-Offset2S-N>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-15</q-QualMin>
                            <q-RxlevMin>-51</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>495</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>30</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <q-Offset1S-N>-40</q-Offset1S-N>
                        <q-Offset2S-N>-40</q-Offset2S-N>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-15</q-QualMin>
                            <q-RxlevMin>-51</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                  <NewInterFreqCellSI-ECN0>
                    <cellInfo>
                      <modeSpecificInfo>
                        <fdd>
                          <primaryCPICH-Info>
                            <primaryScramblingCode>503</primaryScramblingCode>
                          </primaryCPICH-Info>
                          <primaryCPICH-TX-Power>30</primaryCPICH-TX-Power>
                          <readSFN-Indicator>
                            <true/>
                          </readSFN-Indicator>
                          <tx-DiversityIndicator>
                            <false/>
                          </tx-DiversityIndicator>
                        </fdd>
                      </modeSpecificInfo>
                      <cellSelectionReselectionInfo>
                        <q-Offset1S-N>-40</q-Offset1S-N>
                        <q-Offset2S-N>-40</q-Offset2S-N>
                        <modeSpecificInfo>
                          <fdd>
                            <q-QualMin>-15</q-QualMin>
                            <q-RxlevMin>-51</q-RxlevMin>
                          </fdd>
                        </modeSpecificInfo>
                      </cellSelectionReselectionInfo>
                    </cellInfo>
                  </NewInterFreqCellSI-ECN0>
                </newInterFreqCellList>
              </interFreqCellInfoSI-List>
            </interFreqMeasurementSysInfo>
          </cpich-Ec-N0>
        </cellSelectQualityMeasure>
        <interRATMeasurementSysInfo>
          <interRATCellInfoList>
            <removedInterRATCellList>
              <removeNoInterRATCells/>
            </removedInterRATCellList>
            <newInterRATCellList>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>010</ncc>
                      <bcc>100</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>776</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>110</ncc>
                      <bcc>111</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>805</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>101</ncc>
                      <bcc>110</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>795</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>111</ncc>
                      <bcc>101</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>784</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>100</ncc>
                      <bcc>100</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>782</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>011</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>783</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>000</ncc>
                      <bcc>011</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>787</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>111</ncc>
                      <bcc>111</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>797</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>101</ncc>
                      <bcc>000</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>791</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>010</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>786</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>000</ncc>
                      <bcc>100</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>765</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>010</ncc>
                      <bcc>110</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>779</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>001</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>764</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>101</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>810</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>110</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>799</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>001</ncc>
                      <bcc>101</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>765</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
              <NewInterRATCell-B>
                <technologySpecificInfo>
                  <gsm>
                    <cellSelectionReselectionInfo>
                      <q-Offset1S-N>8</q-Offset1S-N>
                      <modeSpecificInfo>
                        <gsm>
                          <q-RxlevMin>-51</q-RxlevMin>
                        </gsm>
                      </modeSpecificInfo>
                    </cellSelectionReselectionInfo>
                    <interRATCellIndividualOffset>0</interRATCellIndividualOffset>
                    <bsic>
                      <ncc>010</ncc>
                      <bcc>100</bcc>
                    </bsic>
                    <frequency-band>
                      <pcs1900BandUsed/>
                    </frequency-band>
                    <bcch-ARFCN>781</bcch-ARFCN>
                  </gsm>
                </technologySpecificInfo>
              </NewInterRATCell-B>
            </newInterRATCellList>
          </interRATCellInfoList>
        </interRATMeasurementSysInfo>
      </hcs-not-used>
    </use-of-HCS>
  </measurementControlSysInfo>
</SysInfoType11>

<encoding>
9BE0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1247</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9C0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1248</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9C2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1249</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9C4E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1250</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
9C63E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1251</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110110000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9C8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1252</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9CA5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1253</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9CC7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1254</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9CEE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1255</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
9D0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1256</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9D2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1257</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9D40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1258</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9D60
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1259</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9D8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1260</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9DA0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1261</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9DC0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1262</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9DE0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1263</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9E0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1264</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9E2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1265</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9E4E0184 E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1266</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType1/>
          </sib-Type>
          <sib-Data-variable>1110011000111010001001010000100001010000000010110001000001110000000101001100001100000010100001011100010111000010101011010000100001000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
E63A2508 500B1070 14C30285 C5C2AD08 40
</encoding>

<SysInfoType1>
  <cn-CommonGSM-MAP-NAS-SysInfo>8E89</cn-CommonGSM-MAP-NAS-SysInfo>
  <cn-DomainSysInfoList>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <cs-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0A01</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
    <CN-DomainSysInfo>
      <cn-DomainIdentity>
        <ps-domain/>
      </cn-DomainIdentity>
      <cn-Type>
        <gsm-MAP>0701</gsm-MAP>
      </cn-Type>
      <cn-DRX-CycleLengthCoeff>7</cn-DRX-CycleLengthCoeff>
    </CN-DomainSysInfo>
  </cn-DomainSysInfoList>
  <ue-ConnTimersAndConstants>
    <t-302>
      <ms1000/>
    </t-302>
    <n-302>6</n-302>
    <t-308>
      <ms40/>
    </t-308>
    <t-309>6</t-309>
    <n-313>
      <s100/>
    </n-313>
    <t-315>
      <s0/>
    </t-315>
  </ue-ConnTimersAndConstants>
  <ue-IdleTimersAndConstants>
    <t-300>
      <ms2000/>
    </t-300>
    <n-300>5</n-300>
    <t-312>10</t-312>
    <n-312>
      <s1/>
    </n-312>
  </ue-IdleTimersAndConstants>
  <v3a0NonCriticalExtensions>
    <sysInfoType1-v3a0ext>
      <ue-ConnTimersAndConstants-v3a0ext/>
      <ue-IdleTimersAndConstants-v3a0ext/>
    </sysInfoType1-v3a0ext>
    <v860NonCriticalExtentions>
      <sysInfoType1-v860ext>
        <ue-ConnTimersAndConstants>
          <t-323>
            <s0/>
          </t-323>
        </ue-ConnTimersAndConstants>
      </sysInfoType1-v860ext>
    </v860NonCriticalExtentions>
  </v3a0NonCriticalExtensions>
</SysInfoType1>

<encoding>
9E63E223 3C3AFFFF 03FFFC50 10F0290C 0A801800 0C8BF1B1 5E200000 03F6C0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1267</sfn-Prime>
    <payload>
      <firstSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <seg-Count>3</seg-Count>
        <sib-Data-fixed>001000110011110000111010111111111111111100000011111111111111110001010000000100001111000000101001000011000000101010000000000110000000000000001100100010111111000110110001010111100010000000000000000000000000001111110110110000</sib-Data-fixed>
      </firstSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9E8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1268</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9EA5E02F 9C000091 E2508050 00041100 18088B95 8C031808 0B000305 33000C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1269</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>1</segmentIndex>
        <sib-Data-fixed>001011111001110000000000000000001001000111100010010100001000000001010000000000000000010000010001000000000001100000001000100010111001010110001100000000110001100000001000000010110000000000000011000001010011001100000000000011</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9EC7E16C C4300B6D 81002184 4A058586 2F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1270</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType5bis/>
        </sib-Type>
        <segmentIndex>2</segmentIndex>
        <sib-Data-variable>1100010000110000000010110110110110000001000000000010000110000100010010100000010110000101100001100010111101000</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9EEE0C43 B20111D0 24541A43 A0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1271</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType12/>
          </sib-Type>
          <sib-Data-variable>10110010000000010001000111010000001001000101010000011010010000111010</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
B20111D0 24541A43 A0
</encoding>

<SysInfoType12>
  <fach-MeasurementOccasionInfo>
    <fACH-meas-occasion-coeff>3</fACH-meas-occasion-coeff>
    <inter-freq-FDD-meas-ind>
      <false/>
    </inter-freq-FDD-meas-ind>
    <inter-freq-TDD-meas-ind>
      <false/>
    </inter-freq-TDD-meas-ind>
    <inter-RAT-meas-ind>
      <gsm/>
    </inter-RAT-meas-ind>
  </fach-MeasurementOccasionInfo>
  <measurementControlSysInfo>
    <use-of-HCS>
      <hcs-not-used>
        <cellSelectQualityMeasure>
          <cpich-Ec-N0/>
        </cellSelectQualityMeasure>
      </hcs-not-used>
    </use-of-HCS>
    <trafficVolumeMeasSysInfo>
      <trafficVolumeMeasurementObjectList>
        <rachorcpch/>
      </trafficVolumeMeasurementObjectList>
      <trafficVolumeMeasQuantity>
        <rlc-BufferPayload/>
      </trafficVolumeMeasQuantity>
      <trafficVolumeReportingQuantity>
        <rlc-RB-BufferPayload>
          <true/>
        </rlc-RB-BufferPayload>
        <rlc-RB-BufferPayloadAverage>
          <false/>
        </rlc-RB-BufferPayloadAverage>
        <rlc-RB-BufferPayloadVariance>
          <false/>
        </rlc-RB-BufferPayloadVariance>
      </trafficVolumeReportingQuantity>
      <measurementValidity>
        <ue-State>
          <all-But-Cell-DCH/>
        </ue-State>
      </measurementValidity>
      <measurementReportingMode>
        <measurementReportTransferMode>
          <acknowledgedModeRLC/>
        </measurementReportTransferMode>
        <periodicalOrEventTrigger>
          <eventTrigger/>
        </periodicalOrEventTrigger>
      </measurementReportingMode>
      <reportCriteriaSysInf>
        <trafficVolumeReportingCriteria>
          <transChCriteriaList>
            <TransChCriteria>
              <ul-transportChannelID>
                <rachorcpch/>
              </ul-transportChannelID>
              <eventSpecificParameters>
                <TrafficVolumeEventParam>
                  <eventID>
                    <e4a/>
                  </eventID>
                  <reportingThreshold>
                    <th1024/>
                  </reportingThreshold>
                  <pendingTimeAfterTrigger>
                    <ptat1/>
                  </pendingTimeAfterTrigger>
                </TrafficVolumeEventParam>
              </eventSpecificParameters>
            </TransChCriteria>
          </transChCriteriaList>
        </trafficVolumeReportingCriteria>
      </reportCriteriaSysInf>
    </trafficVolumeMeasSysInfo>
  </measurementControlSysInfo>
</SysInfoType12>

<encoding>
9F0E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1272</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9F2E2367 1793DF24 1B5A0506 9030010C A7388008 00
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1273</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType3/>
          </sib-Type>
          <sib-Data-variable>00010111100100111101111100100100000110110101101000000101000001101001000000110000000000010000110010100111</sib-Data-variable>
        </CompleteSIBshort>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType7/>
          </sib-Type>
          <sib-Data-variable>00000001000000000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
1793DF24 1B5A0506 9030010C A7
</encoding>

<SysInfoType3>
  <sib4indicator>
    <false/>
  </sib4indicator>
  <cellIdentity>0101111001001111011111001001</cellIdentity>
  <cellSelectReselectInfo>
    <cellSelectQualityMeasure>
      <cpich-Ec-N0/>
    </cellSelectQualityMeasure>
    <modeSpecificInfo>
      <fdd>
        <s-Intrasearch>5</s-Intrasearch>
        <s-Intersearch>4</s-Intersearch>
        <rat-List>
          <RAT-FDD-Info>
            <rat-Identifier>
              <gsm/>
            </rat-Identifier>
            <s-SearchRAT>0</s-SearchRAT>
            <s-HCS-RAT>-1</s-HCS-RAT>
            <s-Limit-SearchRAT>0</s-Limit-SearchRAT>
          </RAT-FDD-Info>
        </rat-List>
        <q-QualMin>-18</q-QualMin>
        <q-RxlevMin>-58</q-RxlevMin>
      </fdd>
    </modeSpecificInfo>
    <q-Hyst-l-S>1</q-Hyst-l-S>
    <t-Reselection-S>1</t-Reselection-S>
    <maxAllowedUL-TX-Power>24</maxAllowedUL-TX-Power>
  </cellSelectReselectInfo>
  <cellAccessRestriction>
    <cellBarred>
      <notBarred/>
    </cellBarred>
    <cellReservedForOperatorUse>
      <notReserved/>
    </cellReservedForOperatorUse>
    <cellReservationExtension>
      <notReserved/>
    </cellReservationExtension>
  </cellAccessRestriction>
</SysInfoType3>

<encoding>
9F40
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1274</sfn-Prime>
    <payload>
      <noSegment/>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9F6E0213 03AEB0
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1275</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <systemInformationBlockType2/>
          </sib-Type>
          <sib-Data-variable>00000011101011101011</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
9F8E00BB 40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1276</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>01000000110001000010010011000000010110101100001011111110000000100001000011001000010000000100011110110100010001100110011000001000101010001110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
40C424C0 5AC2FE02 10C84047 B4466608 A8E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>5</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>3</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>34</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5bis>3</sysInfoType5bis>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>1</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
9FAE1F87 41FD0813 ABA40470 11FB1011 C08CAE40 47
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>1277</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <extensionType/>
          </sib-Type>
          <sib-Data-variable>0100000111111101000010000001001110101011101001000000010001110000000100011111101100010000000100011100000010001100101011100100000001000111</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
41FD0813 ABA40470 11FB1011 C08CAE40 47
</encoding>

<SysInfoType19>
  <utra-PriorityInfoList>
    <utra-ServingCell>
      <priority>3</priority>
      <s-PrioritySearch1>31</s-PrioritySearch1>
      <threshServingLow>8</threshServingLow>
    </utra-ServingCell>
  </utra-PriorityInfoList>
  <eutra-FrequencyAndPriorityInfoList>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>5035</earfcn>
      <priority>5</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>1150</earfcn>
      <priority>6</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
    <EUTRA-FrequencyAndPriorityInfo>
      <earfcn>2250</earfcn>
      <priority>7</priority>
      <qRxLevMinEUTRA>-62</qRxLevMinEUTRA>
      <threshXhigh>1</threshXhigh>
      <threshXlow>3</threshXlow>
      <eutraDetection>
        <true/>
      </eutraDetection>
    </EUTRA-FrequencyAndPriorityInfo>
  </eutra-FrequencyAndPriorityInfoList>
</SysInfoType19>

