<encoding>
3B8E00BB 30C424C0 5A82FE00 A0C84044 84466608 A9E14A70 100A0100 
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>476</sfn-Prime>
    <payload>
      <completeSIB-List>
        <CompleteSIBshort>
          <sib-Type>
            <masterInformationBlock/>
          </sib-Type>
          <sib-Data-variable>00110000110001000010010011000000010110101000001011111110000000001010000011001000010000000100010010000100010001100110011000001000101010011110000101001010011100000001000000001010000000010000</sib-Data-variable>
        </CompleteSIBshort>
      </completeSIB-List>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
30C424C0 5A82FE00 A0C84044 84466608 A9E14A70 100A0100 
</encoding>

<MasterInformationBlock>
  <mib-ValueTag>4</mib-ValueTag>
  <plmn-Type>
    <gsm-MAP>
      <plmn-Identity>
        <mcc>
          <Digit>3</Digit>
          <Digit>1</Digit>
          <Digit>0</Digit>
        </mcc>
        <mnc>
          <Digit>2</Digit>
          <Digit>6</Digit>
          <Digit>0</Digit>
        </mnc>
      </plmn-Identity>
    </gsm-MAP>
  </plmn-Type>
  <sibSb-ReferenceList>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoTypeSB1>1</sysInfoTypeSB1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep128>63</rep128>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType1>11</sysInfoType1>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep32>2</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType3>1</sysInfoType3>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType5>1</sysInfoType5>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>3</segCount>
          <sib-Pos>
            <rep32>3</rep32>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType7/>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <sib-Pos>
            <rep16>1</rep16>
          </sib-Pos>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
    <SchedulingInformationSIBSb>
      <sibSb-Type>
        <sysInfoType11>2</sysInfoType11>
      </sibSb-Type>
      <scheduling>
        <scheduling>
          <segCount>9</segCount>
          <sib-Pos>
            <rep128>10</rep128>
          </sib-Pos>
          <sib-PosOffsetInfo>
            <so2/>
            <so4/>
            <so2/>
            <so2/>
            <so22/>
            <so2/>
            <so4/>
            <so2/>
          </sib-PosOffsetInfo>
        </scheduling>
      </scheduling>
    </SchedulingInformationSIBSb>
  </sibSb-ReferenceList>
</MasterInformationBlock>

<encoding>
3BA4B68B C0028734 7651E011 439A3B23 B250A1CC 64864D14 39A3B2D7 240A1C
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>477</sfn-Prime>
    <payload>
      <subsequentSegment>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>7</segmentIndex>
        <sib-Data-fixed>100010111100000000000010100001110011010001110110010100011110000000010001010000111001101000111011001000111011001001010000101000011100110001100100100001100100110100010100001110011010001110110010110101110010010000001010000111</sib-Data-fixed>
      </subsequentSegment>
    </payload>
  </message>
</BCCH-BCH-Message>

<encoding>
3BC6B754 347654E6 21439A3B 213270
</encoding>

<BCCH-BCH-Message>
  <message>
    <sfn-Prime>478</sfn-Prime>
    <payload>
      <lastSegmentShort>
        <sib-Type>
          <systemInformationBlockType11/>
        </sib-Type>
        <segmentIndex>8</segmentIndex>
        <sib-Data-variable>0011010001110110010101001110011000100001010000111001101000111011001000010011001001110</sib-Data-variable>
      </lastSegmentShort>
    </payload>
  </message>
</BCCH-BCH-Message>

