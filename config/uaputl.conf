#   File : uaputl.conf


ap_config={
    SSID="WGS-8000"         # SSID of Micro AP, use '\' as an escape character before '"' character in SSID
    BeaconPeriod=100                # Beacon period in TU
    Channel=6                       # Radio Channel	6
#   Channel=0,1                     # auto channel select (MCS mode)
#   Channel=6,2                     # primary channel 6, secondary channel above.
#   Channel=6,4                     # primary channel 6, secondary channel below
    ChanList=1,6,11                 # Scan channel list 
#   AP_MAC=00:34:22:77:46:41        # MAC address of AP
    Band=0                          # 0 for 2.4GHz band
                                    # 1 for 5GHz band   
    RxAntenna=0                     # 0 for Antenna A
                                    # 1 for Antenna B
    TxAntenna=0                     # 0 for Antenna A 
                                    # 1 for Antenna B
    Rate=0x82,0x84,0x8b,0x96,0x0C,0x12,0x18,0x24,0x30,0x48,0x60,0x6c  
                                    # Set of data rate that a station
                                    # in the BSS may use
                                    # (in unit of 500 kilobit/s)
    TxPowerLevel=13                 # Transmit power level in dBm
    BroadcastSSID=1                 # Broadcast SSID feature
                                    # 1: Enable  0:Disable
    RTSThreshold=2347               # RTS threshold value
    FragThreshold=2346              # Fragmentation threshold value
    DTIMPeriod=1                    # DTIM period in beacon periods 
    MCBCdataRate=0                  # MCBC rate to use for packet transmission
                                    # 0:auto
                                    # >0 fix rate (in unit of 500 kilobit/s) 	 
    PktFwdCtl=1                     # Packet forwarding control
                                    # 0: forward all packets to the host
                                    # 1: firmware handles intr-BSS packets
    StaAgeoutTimer=1800             # Inactive client station age out timer value
                                    # in units of 100ms
    PSStaAgeoutTimer=400            # Inactive client PS station age out timer value
                                    # in units of 100ms
    MaxStaNum=10                    # Max number of stations allowed to connect
    Retrylimit=7                    # Retry limit to use for packet transmissions
    AuthMode=0                      # 0:Open authentication
                                    # 1:shared key authentication
    Protocol=32                      # protocol to use
                                    # 1: No security  	2: Static WEP
                                    # 8: WPA	32: WPA2    40:WPA2 Mixed Mode
    RSNReplayProtection=0           # RSN replay protection 0: disabled, 1: enabled
    PairwiseUpdateTimeout=100       #Pairwise Handshake update timeout: 100ms
    PairwiseHandshakeRetries=3      #Pairwise Handshake retries: 3
    GroupwiseUpdateTimeout=100      #Groupwise Handshake update timeout: 100ms
    GroupwiseHandshakeRetries=3           #Groupwise Handshake retries: 3

# **** WEP security setting******
#   KeyIndex=0                      # Index of WEP key to use (0 to 3)
#   Sample Key configurations are 
#       Key_0="55555"			
#       Key_1=1234567890
#       Key_2="1234567890123"
#       Key_3=12345678901234567890123456

# **** Pairwise Cipher setting******
#    Configure both of the following for Protocol=40
    PwkCipherWPA=8                 # Pairwise cipher type
                                    # 4: TKIP     8: AES CCMP
                                    # 12: AES CCMP + TKIP
   PwkCipherWPA2=8                # Pairwise cipher type
                                    # 4: TKIP     8: AES CCMP
                                    # 12: AES CCMP + TKIP                                    

# **** Group Cipher setting******

    GwkCipher=8                    # group cipher type
                                    # 4: TKIP     8: AES CCMP

    PSK="1234567890"               # WPA/WPA2 passphrase 	
     GroupRekeyTime= 86400          # Group key re-key interval, in second.
                                    # 0 mean never re-key

    Enable11n=1                     # 1 to enable, 0 to disable 
    HTCapInfo=0x111c                # HTCapInfo 
                                    # Bit 15-13: Reserved set to 0
                                    # Bit 12: DSS/CCK mode in 40MHz enable/disable
                                    # Bit 11-10: Reserved set to 0
                                    # Bit 9-8: Reserved set to 0x01
                                    # Bit 7: Reserved set to 0
                                    # Bit 6: Short GI in 40 Mhz enable/disable
                                    # Bit 5: Short GI in 20 Mhz enable/disable
                                    # Bit 4: Green field enable/disble
                                    # Bit 3-2: Reserved set to 1
                                    # Bit 1: 20/40 Mhz enable disable.
                                    # Bit 0: Reserved set to 0
    AMPDU=0x03                      # AMPDU
                                    # Bit 7-5: Reserved set to 0
                                    # Bit 4-2: Minimum MPDU Start spacing
                                    #          Set to 0 for no restriction
                                    #          Set to 1 for 1/4 us
                                    #          Set to 2 for 1/2 us
                                    #          Set to 3 for 1 us
                                    #          Set to 4 for 2 us
                                    #          Set to 5 for 4 us
                                    #          Set to 6 for 8 us
                                    #          Set to 7 for 16 us
                                    # Bit 1-0: Max A-MPDU length
#    Enable2040Coex=1                #Enable 20/40 coex feature

                                    #802.11D specific configuration
    11d_enable=0                    # 0-disable 1-enable
#   country=US                      # country information

}



ap_mac_filter={
    FilterMode=0                    # Mode of filter table
                                    # 0: filter table is disabled
                                    # 1: allow MAC address in the filter table to associate
                                    # 2: block MAC address in the filter table       
    Count=0                         # Number of entries in filter table,up to 16
#Sample mac settings are 
#    mac_1=00:50:23:45:76:22         # mac address
#    mac_2=00:34:22:77:46:34         # mac address   	
}

# Wmm param setting
Wmm_parameters={
    Qos_info=0x80
    AC_BE
    Aifsn=1
    Ecw_max=1
    Ecw_min=1
    Tx_op=1
    AC_BK
    Aifsn=2
    Ecw_max=2
    Ecw_min=2
    Tx_op=2
    AC_VI
    Aifsn=3
    Ecw_max=3
    Ecw_min=3
    Tx_op=3
    AC_VO
    Aifsn=4
    Ecw_max=4
    Ecw_min=4
    Tx_op=4
}

